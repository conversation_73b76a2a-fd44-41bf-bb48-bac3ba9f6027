# HTTP媒体类型不支持异常解决方案

## 问题描述

项目持续出现以下异常：
```
[2025-05-29 16:19:15.943] ERROR [http-nio-8085-exec-3] GlobalExceptionTranslator.java:80 - Internal Server Error
org.springframework.web.HttpMediaTypeNotSupportedException: Content type 'application/octet-stream;charset=UTF-8' not supported
```

## 问题根本原因

1. **客户端错误设置Content-Type**：客户端将Content-Type设置为`application/octet-stream;charset=UTF-8`
2. **Spring Boot默认不支持**：Spring Boot的默认JSON消息转换器不支持`application/octet-stream`类型
3. **异常处理不完善**：原有的异常处理器没有专门处理`HttpMediaTypeNotSupportedException`

## 解决方案

### 1. 增强异常处理器

#### 1.1 更新nevmp-service模块的GlobalExceptionTranslator
- 添加了`HttpMediaTypeNotSupportedException`的专门处理方法
- 提供详细的错误信息和请求上下文
- 使用WARN级别日志记录，避免ERROR日志污染

#### 1.2 创建nevmp-public专用异常处理器
- 文件：`PublicExceptionTranslator.java`
- 专门处理公共接口的特殊异常情况
- 记录客户端IP、User-Agent等详细信息用于排查

### 2. 扩展媒体类型支持

#### 2.1 创建WebMvcConfiguration
- 文件：`WebMvcConfiguration.java`
- 扩展JSON转换器支持`application/octet-stream`类型
- **注意**：这是临时解决方案，建议客户端修正Content-Type

### 3. 诊断工具

#### 3.1 创建DiagnosticController
- 文件：`DiagnosticController.java`
- 提供系统媒体类型查询接口：`GET /diagnostic/supported-media-types`
- 提供请求信息测试接口：`POST /diagnostic/test-request`
- 提供健康检查接口：`GET /diagnostic/health`

## 使用指南

### 排查问题步骤

1. **查看新的异常日志**：
   - 现在会记录详细的请求信息（URI、Content-Type、客户端IP等）
   - 日志级别为WARN，不再污染ERROR日志

2. **使用诊断接口**：
   ```bash
   # 查看系统支持的媒体类型
   curl -X GET http://localhost:8085/diagnostic/supported-media-types
   
   # 测试客户端请求
   curl -X POST http://localhost:8085/diagnostic/test-request \
        -H "Content-Type: application/octet-stream" \
        -d '{"test": "data"}'
   ```

3. **分析客户端行为**：
   - 检查客户端设置的Content-Type是否正确
   - 确认请求体内容与Content-Type是否匹配

### 客户端修正建议

1. **JSON数据请求**：
   ```javascript
   // 正确设置
   fetch('/api/endpoint', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json'
     },
     body: JSON.stringify(data)
   });
   ```

2. **文件上传请求**：
   ```javascript
   // 文件上传使用multipart/form-data
   const formData = new FormData();
   formData.append('file', file);
   formData.append('type', '1');
   
   fetch('/secondHandVehicle/upload', {
     method: 'POST',
     body: formData  // 不要设置Content-Type，让浏览器自动设置
   });
   ```

## 监控建议

1. **日志监控**：
   - 监控WARN级别的"不支持的媒体类型请求"日志
   - 分析客户端IP和User-Agent模式

2. **指标监控**：
   - 统计HttpMediaTypeNotSupportedException的发生频率
   - 按客户端IP分组分析异常来源

3. **告警设置**：
   - 当异常频率超过阈值时发送告警
   - 提醒开发团队检查客户端实现

## 长期解决方案

1. **客户端修正**：
   - 与前端/客户端团队协作修正Content-Type设置
   - 统一API调用规范

2. **API文档完善**：
   - 明确每个接口支持的Content-Type
   - 提供正确的调用示例

3. **接口测试**：
   - 增加Content-Type相关的测试用例
   - 确保接口对各种媒体类型的处理正确

## 注意事项

1. **安全考虑**：
   - 扩展媒体类型支持可能带来安全风险
   - 建议仅作为临时解决方案

2. **性能影响**：
   - 新增的异常处理和日志记录对性能影响很小
   - 诊断接口仅用于排查，不建议在生产环境频繁调用

3. **向后兼容**：
   - 所有修改都保持向后兼容
   - 不影响现有正常的API调用
