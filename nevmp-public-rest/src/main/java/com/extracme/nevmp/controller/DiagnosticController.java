package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 诊断控制器
 * 用于排查HTTP媒体类型和请求相关问题
 * 
 * <AUTHOR>
 * @Description 系统诊断接口
 */
@Api(tags = "系统诊断接口")
@RestController
@RequestMapping("/diagnostic")
public class DiagnosticController {

    private static final Logger logger = LoggerFactory.getLogger(DiagnosticController.class);
    
    private final RequestMappingHandlerAdapter handlerAdapter;

    public DiagnosticController(RequestMappingHandlerAdapter handlerAdapter) {
        this.handlerAdapter = handlerAdapter;
    }

    /**
     * 获取系统支持的媒体类型信息
     * 用于排查HttpMediaTypeNotSupportedException问题
     */
    @ApiOperation(value = "获取系统支持的媒体类型", notes = "返回当前系统配置的HTTP消息转换器及其支持的媒体类型")
    @GetMapping("/supported-media-types")
    public BaseResponse getSupportedMediaTypes() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<HttpMessageConverter<?>> converters = handlerAdapter.getMessageConverters();
            
            Map<String, List<String>> converterInfo = converters.stream()
                    .collect(Collectors.toMap(
                            converter -> converter.getClass().getSimpleName(),
                            converter -> converter.getSupportedMediaTypes().stream()
                                    .map(MediaType::toString)
                                    .collect(Collectors.toList()),
                            (existing, replacement) -> existing // 处理重复键
                    ));
            
            result.put("messageConverters", converterInfo);
            result.put("totalConverters", converters.size());
            
            logger.info("系统媒体类型查询成功，共{}个转换器", converters.size());
            
        } catch (Exception e) {
            logger.error("获取系统媒体类型信息失败", e);
            result.put("error", "获取信息失败: " + e.getMessage());
        }
        
        return BaseResponse.builder()
                .data(result)
                .build();
    }

    /**
     * 测试请求信息
     * 用于分析客户端发送的请求头信息
     */
    @ApiOperation(value = "测试请求信息", notes = "返回客户端发送的详细请求信息，用于排查问题")
    @PostMapping("/test-request")
    public BaseResponse testRequest(HttpServletRequest request, @RequestBody(required = false) Object body) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 请求基本信息
            result.put("method", request.getMethod());
            result.put("uri", request.getRequestURI());
            result.put("contentType", request.getContentType());
            result.put("contentLength", request.getContentLength());
            result.put("characterEncoding", request.getCharacterEncoding());
            result.put("clientIP", getClientIpAddress(request));
            
            // 请求头信息
            Map<String, String> headers = new HashMap<>();
            request.getHeaderNames().asIterator().forEachRemaining(headerName -> 
                    headers.put(headerName, request.getHeader(headerName)));
            result.put("headers", headers);
            
            // 请求体信息
            if (body != null) {
                result.put("bodyType", body.getClass().getSimpleName());
                result.put("bodyContent", body.toString());
            } else {
                result.put("bodyType", "null");
                result.put("bodyContent", "无请求体或解析失败");
            }
            
            logger.info("请求信息测试 - URI: {}, Content-Type: {}, 客户端IP: {}", 
                    request.getRequestURI(), request.getContentType(), getClientIpAddress(request));
            
        } catch (Exception e) {
            logger.error("测试请求信息失败", e);
            result.put("error", "处理失败: " + e.getMessage());
        }
        
        return BaseResponse.builder()
                .data(result)
                .build();
    }

    /**
     * 健康检查接口
     */
    @ApiOperation(value = "健康检查", notes = "简单的健康检查接口")
    @GetMapping("/health")
    public BaseResponse health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("service", "nevmp-public");
        
        return BaseResponse.builder()
                .data(result)
                .build();
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
