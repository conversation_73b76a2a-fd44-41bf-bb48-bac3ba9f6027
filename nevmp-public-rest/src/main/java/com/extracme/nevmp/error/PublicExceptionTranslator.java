package com.extracme.nevmp.error;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * nevmp-public项目专用异常处理器
 * 用于处理公共接口的特殊异常情况
 * 
 * <AUTHOR>
 * @Description 公共接口异常处理转化类
 */
@RestControllerAdvice
public class PublicExceptionTranslator {

    private static final Logger logger = LoggerFactory.getLogger(PublicExceptionTranslator.class);

    /**
     * 处理HTTP媒体类型不支持异常
     * 当客户端发送的Content-Type不被服务端支持时触发
     * 常见场景：客户端错误设置Content-Type为application/octet-stream但发送JSON数据
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public BaseResponse handleMediaTypeNotSupported(HttpServletRequest request, HttpMediaTypeNotSupportedException e) {
        // 记录详细的请求信息用于排查
        logger.warn("收到不支持的媒体类型请求 - URI: {}, Method: {}, Content-Type: {}, 客户端IP: {}, User-Agent: {}, 支持的媒体类型: {}", 
                request.getRequestURI(),
                request.getMethod(),
                e.getContentType() != null ? e.getContentType().toString() : "未设置",
                getClientIpAddress(request),
                request.getHeader("User-Agent"),
                e.getSupportedMediaTypes());
        
        // 构建友好的错误消息
        String contentType = e.getContentType() != null ? e.getContentType().toString() : "未知";
        String message = String.format("不支持的媒体类型: %s。请检查请求头Content-Type设置，常用类型：application/json", contentType);
        
        return BaseResponse
                .builder()
                .resultCode(ResultCode.PARAM_TYPE_ERROR)
                .message(message)
                .build();
    }

    /**
     * 处理HTTP请求方法不支持异常
     * 当客户端使用了接口不支持的HTTP方法时触发
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public BaseResponse handleMethodNotSupported(HttpServletRequest request, HttpRequestMethodNotSupportedException e) {
        logger.warn("收到不支持的请求方法 - URI: {}, Method: {}, 客户端IP: {}, 支持的方法: {}", 
                request.getRequestURI(),
                request.getMethod(),
                getClientIpAddress(request),
                e.getSupportedMethods());
        
        String message = String.format("不支持的请求方法: %s，支持的方法: %s", 
                request.getMethod(), 
                String.join(", ", e.getSupportedMethods()));
        
        return BaseResponse
                .builder()
                .resultCode(ResultCode.PARAM_VALID_ERROR)
                .message(message)
                .build();
    }

    /**
     * 获取客户端真实IP地址
     * 考虑代理和负载均衡的情况
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
