package com.extracme.nevmp.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * Web MVC配置类
 * 用于配置HTTP消息转换器和其他Web相关设置
 * 
 * <AUTHOR>
 * @Description Web MVC配置
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    /**
     * 配置HTTP消息转换器
     * 扩展JSON转换器支持的媒体类型，以处理客户端可能发送的特殊Content-Type
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 创建自定义的JSON转换器
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        
        // 获取默认支持的媒体类型
        List<MediaType> supportedMediaTypes = new ArrayList<>(jsonConverter.getSupportedMediaTypes());
        
        // 添加对application/octet-stream的支持（谨慎使用）
        // 注意：这样做可能会带来安全风险，建议客户端修正Content-Type
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        
        // 添加对text/plain的支持（某些客户端可能使用）
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        
        // 设置支持的媒体类型
        jsonConverter.setSupportedMediaTypes(supportedMediaTypes);
        
        // 将自定义转换器添加到转换器列表的开头，确保优先使用
        converters.add(0, jsonConverter);
    }
}
