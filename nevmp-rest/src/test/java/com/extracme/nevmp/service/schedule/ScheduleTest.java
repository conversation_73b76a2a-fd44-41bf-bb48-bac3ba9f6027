package com.extracme.nevmp.service.schedule;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.traffic.TrafficPushService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@DisplayName("owner qualification service test")
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.NONE)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ScheduleTest {

    @Autowired
    private OwnerService ownerService;

    @Autowired
    private TrafficPushService trafficPushService;


    @Test
    public void autoSyncVehicleModelFirstRegTimeTest(){
        ownerService.dealOmitVehicleConfirmTask();
    }


    @Test
    public void trafficPushDataTest(){
        trafficPushService.pushData();
    }


}
