package com.extracme.nevmp.service.qulification;

import static org.junit.Assert.assertEquals;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.extracme.nevmp.service.qualification.impl.NewCreditServiceImpl;


/**
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.NONE)
public class OwnerQualificationCreditTest {

    @Autowired
    private NewCreditServiceImpl newCreditService;


    @Test
    public void testPersonalCredit(){
        int result = newCreditService.reviewPersonal("王奕仁","330425197011061333");
        assertEquals(result, 1);
    }

    @Test
    public void testPersonalCreditFail(){
        int result = newCreditService.reviewPersonal("金永年", "310224196409280012");
        assertEquals(result, 2);
    }

    @Test
    public void testPersonalCreditFail2(){
        int result = newCreditService.reviewPersonal("陈玉", "310226199712111314");
        assertEquals(result, 2);
    }

    @Test
    public void testBusinessCredit(){
        int result = newCreditService.reviewBusiness("上海交通大学","1210000042500615X0");
        assertEquals(result, 1);
    }

    @Test void testBusinessCreditFail(){
        int result = newCreditService.reviewBusiness("上海艺湛科技有限公司","913101153508749202");
        assertEquals(result, 2);
    }

    @Test void testBusinessCreditFail2(){
        int result = newCreditService.reviewBusiness("上海安抵运输有限公司","91310113785642045B");
        assertEquals(result, 2);
    }

    @Test
    public void testBusinessCreditNameError(){
        int result = newCreditService.reviewBusiness("上海未石影视文化有限公司11","91310115301447869C");
        assertEquals(result, 4);
    }

    @Test
    public void testBusinessCreditAuthIdError(){
        int result = newCreditService.reviewBusiness("上海未石影视文化有限公司","91310115301447869CABC");
        assertEquals(result, 3);
    }
}
