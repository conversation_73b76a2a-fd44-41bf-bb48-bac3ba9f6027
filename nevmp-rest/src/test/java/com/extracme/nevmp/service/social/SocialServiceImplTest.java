package com.extracme.nevmp.service.social;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.extracme.nevmp.constant.ApiConst;
import com.extracme.nevmp.dto.cert.CheckIDCardResponse;
import com.extracme.nevmp.dto.cert.SecQueryCertBaseDataResponse;
import com.extracme.nevmp.dto.common.FileInfoDTO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.gov.traffic.DriverLicenseInfo;
import com.extracme.nevmp.dto.gov.traffic.DriverVehicleInfo;
import com.extracme.nevmp.dto.gov.traffic.TrafficBaseResponse;
import com.extracme.nevmp.dto.owner.OwnerConfirmInfoDTO;
import com.extracme.nevmp.dto.owner.SearchOwnerConfirmListDTO;
import com.extracme.nevmp.dto.owner.secondhand.SearchSecondHandVehicleAppliedOwnerDTO;
import com.extracme.nevmp.dto.owner.secondhand.SecondHandVehicleAppliedOwnerDTO;
import com.extracme.nevmp.dto.qualification.owner.DenyOwnerQualificationDTO;
import com.extracme.nevmp.dto.qualification.traffic.OwnerTrafficQualificationDTO;
import com.extracme.nevmp.dto.qualification.traffic.SearchOwnerTrafficQualificationDTO;
import com.extracme.nevmp.enums.FileTypeEnum;
import com.extracme.nevmp.service.FileService;
import com.extracme.nevmp.service.OwnerTrafficQualificationService;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.qualification.DriverService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.service.qualification.ResidencePermitService;
import com.extracme.nevmp.service.qualification.SocialService;
import com.extracme.nevmp.service.traffic.TrafficPushService;
import com.extracme.nevmp.utils.DateUtil;
import com.extracme.nevmp.utils.JsonUtil;
import com.extracme.nevmp.utils.RestTemplateUtil;
import com.extracme.nevmp.utils.ShaUtil;
import com.fasterxml.jackson.core.type.TypeReference;

@SpringBootTest
public class SocialServiceImplTest {

    @Autowired
    private SocialService socialService;

    @Autowired
    private DriverService driverService;

    @Autowired
    private ResidencePermitService residencePermitService;

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @Autowired
    private TrafficPushService trafficPushService;

    @Autowired
    private FileService fileService;

    @Autowired
    private OwnerService ownerService;

    @Autowired
    private OwnerTrafficQualificationService ownerTrafficQualificationService;


    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void permitPoint() {
//        boolean b = socialService.has120ResidencePermitPoints("370304198712012723");
        boolean b = socialService.has120ResidencePermitPoints("******************");
        System.out.println(b);
//        String point = "[{\"idcard\":\"370304198712012723\",\"name\":\"王泽天\",\"validitydayjf\":\"2021-08-21\"}]";
//        List<ResidencePermitPointVO> residencePermitVOs = JSON.parseArray(point, ResidencePermitPointVO.class);
//        System.out.println(JSON.toJSONString(residencePermitVOs));


    }

    @Test
    public void permit() {
//        String name = "吴卫";
//        String authId = "******************";


        //李俞洁  142602199409210062
        //张牧吟   610111199405232029
//        String name = "柯含章";
//        String authId = "35058219940306853X";

        String name = "刘凯祥";
        String authId = "34222419900320183X";

        System.out.println(residencePermitService.hasResidencePermit(name, authId));
    }

    @Test
    public void driver() {
        DriverVehicleInfo driverVehicleInfo = driverService.queryVehicleInfo("31010719840203131X");
        System.out.println(JSON.toJSONString(driverVehicleInfo));

    }

    @Test
    public void social() {
//        socialService.hasTwelveCumulativeSocial("洪尉", 1, "362502199612208019");
//        System.out.println(socialService.hasTwelveCumulativeSocial(" 汪淑晴", 1, "413001199801183027"));
//        System.out.println(socialService.hasSixConsecutiveSocial("桂玥庭", 11, "810000199104270120"));
//        SocialApproveDTO socialApproveDTO = new SocialApproveDTO();
//        socialApproveDTO.setXm("蒋长");
//            socialApproveDTO.setZjlx("01");
////            socialApproveDTO.setZjlx("02");
//        socialApproveDTO.setZjhm("310115199306110418");
//
//        SocialBaseRequest<SocialApproveDTO> request = new SocialBaseRequest<>();
//        request.setData(socialApproveDTO);
//        String responseS = restTemplateUtil.post(ApiConst.Social.CONTINUOUS_PAYMENT_COMPARE_QUERY, request, String.class);
//        System.out.println(ApiConst.Social.CONTINUOUS_PAYMENT_COMPARE_QUERY);
//        System.out.println("SocialBaseResponse|" + responseS);
//        SocialBaseResponse response = JsonUtil.readStr(responseS, SocialBaseResponse.class);
//        if (response.getIsSuccess().equals(true)) {
//            SocialData socialData = response.getData();
//            System.out.println(socialData.getLjBdjg());
//        }
    }

    @Test
    public void socialNew(){
        System.out.println(residencePermitService.hasResidencePermitNew("140212199604050059"));
//        System.out.println(residencePermitService.hasResidencePermitNew("310115199306110418"));
    }

    @Test
    public void deny() {

//        List<Long> list = Arrays.asList(69153L,74014L,75419L,75420L,80103L,82585L,108601L,175898L,175909L,175912L,175913L,175919L,175928L,175932L,175933L,175957L,175959L,175982L,175985L,175989L,176001L,176008L,176022L,176034L,176038L,176041L,176050L,176058L,176060L,176064L,176074L,176083L,176091L,176095L,176096L,176103L,176105L,176110L,176112L,176119L,176130L,176143L,176144L,176150L,176158L,176162L,176164L,176169L,176174L,176177L,176179L,176196L,176198L,176204L,176210L,176214L,176215L,176216L,176218L,176221L,176224L,176230L,176233L,176236L,176240L,176244L,176249L,176250L,176255L,176256L,176261L,176266L,176272L,176276L,176278L,176280L,176284L,176290L,176298L,176305L,176314L,176326L,176328L,176329L,176334L,176347L,176358L,176359L,176369L,176370L,176373L,176387L,176391L,176402L,176407L,176408L,176412L,176413L,176416L,176424L,176426L,176428L,176430L,176437L,176454L,176459L,176464L,176477L,176479L,176482L,176487L,176493L,176495L,176502L,176507L,176511L,176514L,176521L,176538L,176541L,176565L,176580L,176588L,176595L,176597L,176599L,176600L,176603L,176605L,176618L,176625L,176631L,176639L,176641L,176645L,176647L,176658L,176662L,176663L,176664L,176665L,176668L,176687L,176690L,176692L,176699L,176705L,176713L,176728L,176729L,176731L,176737L,176748L,176749L,176750L,176782L,176795L,176808L,176821L,176825L,176844L,176846L,176848L,176854L,176863L,176867L,176869L,176879L,176885L,176886L,176893L,176900L,176902L,176913L,176919L,176920L,176933L,176936L,176939L,176940L,176944L,176952L,176954L,176963L,176973L,176975L,176976L,176977L,176982L,176983L,176984L,176987L,177009L,177011L,177016L,177018L,177024L,177037L,177041L,177049L,177068L,177072L,177074L,177080L,177087L,177094L,177097L,177104L,177108L,177113L,177114L,177127L,177131L,177133L,177139L,177140L,177141L,177142L,177148L,177149L,177150L,177151L,177154L,177158L,177166L,177178L,177181L,177182L,177185L,177194L,177203L,177204L,177214L,177218L,177223L,177241L,177242L,177245L,177246L,177253L,177264L,177265L,177266L,177268L,177280L,177311L,177320L,177335L,177337L,177344L,177345L,177348L,177350L,177353L,177358L,177366L,177382L,177383L,177385L,177390L,177398L,177399L,177405L,177413L,177414L,177421L,177422L,177429L,177435L,177438L,177439L,177449L,177458L,177461L,177472L,177479L,177497L,177499L,177500L,177501L,177525L,177530L,177534L,177536L,177544L,177563L,177565L,177576L,177584L,177588L,177597L,177607L,177615L,177617L,177621L,177626L,177633L,177638L,177655L,177657L,177660L,177662L,177663L,177665L,177666L,177668L,177678L,177679L,177690L,177699L,177701L,177703L,177704L,177712L,177716L,177730L,177737L,177748L,177759L,177763L,177771L,177774L,177779L,177792L,177806L,177814L,177815L,177824L,177842L,177851L,177852L,177857L,177861L,177870L,177884L,177900L,177901L,177903L,177907L,177915L,177919L,177928L,177929L,177938L,177940L,177951L,177952L,177955L,177956L,177962L,177963L,177972L,177973L,177975L,177979L,177980L,177984L,177985L,177988L,177997L,178001L,178009L,178010L,178014L,178026L,178028L,178031L,178032L,178035L,178039L,178041L,178047L,178051L,178058L,178064L,178070L,178083L,178088L,178091L,178100L,178102L,178112L,178114L,178127L,178128L,178130L,178137L,178142L,178144L,178146L,178148L,178149L,178155L,178159L,178165L,178173L,178174L,178182L,178187L,178190L,178194L,178196L,178213L,178214L,178218L,178221L,178224L,178233L,178235L,178237L,178243L,178249L,178254L,178255L,178262L,178263L,178264L,178268L,178269L,178270L,178271L,178274L,178275L,178276L,178282L,178283L,178284L,178287L,178289L,178292L,178294L,178297L,178311L,178314L,178316L,178325L,178327L,178329L,178335L,178339L,178340L,178344L,178345L,178349L,178350L,178352L,178354L,178357L,178359L,178367L,178368L,178370L,178371L,178373L,178381L,178392L,178405L,178408L,178415L,178416L,178423L,178424L,178426L,178431L,178433L,178439L,178444L,178445L,178447L,178449L,178451L,178456L,178461L,178470L,178474L,178482L,178484L,178486L,178494L,178504L,178505L,178507L,178511L,178513L,178528L,178533L,178536L,178548L,178562L,178563L,178564L,178568L,178574L,178583L,178594L,178600L,178606L,178608L,178613L,178617L,178636L,178639L,178649L,178660L,178665L,178668L,178683L,178689L,178690L,178693L,178695L,178696L,178697L,178708L,178709L,178717L,178722L,178725L,178728L,178735L,178755L,178759L,178765L,178771L,178777L,178783L,178785L,178791L,178801L,178805L,178807L,178815L,178818L,178831L,178836L,178840L,178854L,178857L,178860L,178868L,178872L,178874L,178882L,178884L,178885L,178892L,178900L,178908L,178913L,178917L,178937L,178940L,178943L,178948L,178967L,178991L,178992L,178993L,179014L,179016L,179019L,179023L,179027L,179030L,179036L,179037L,179038L,179048L,179051L,179052L,179054L,179056L,179074L,179078L,179082L,179090L,179094L,179100L,179102L,179103L,179127L,179141L,179143L,179156L,179162L,179170L,179172L,179182L,179188L,179200L,179203L,179207L,179209L,179210L,179223L,179224L,179235L,179240L,179247L,179248L,179261L,179265L,179266L,179272L,179276L,179284L,179287L,179288L,179291L,179300L,179307L,179314L,179317L,179318L,179324L,179326L,179336L,179338L,179342L,179343L,179352L,179353L,179362L,179364L,179375L,179376L,179383L,179389L,179404L,179412L,179413L,179414L,179430L,179438L,179451L,179454L,179455L,179463L,179465L,179466L,179467L,179473L,179476L,179480L,179483L,179488L,179491L,179492L,179503L,179506L,179510L,179513L,179517L,179519L,179526L,179530L,179533L,179535L,179536L,179541L,179551L,179553L,179556L,179566L,179570L,179589L,179605L,179617L,179619L,179621L,179629L,179637L,179642L,179643L,179646L,179651L,179654L,179655L,179661L,179670L,179671L,179676L,179686L,179703L,179713L,179716L,179739L,179741L,179760L,179766L,179780L,179782L,179786L,179793L,179799L,179807L,179812L,179814L,179816L,179817L,179818L,179824L,179831L,179832L,179842L,179843L,179850L,179859L,179867L,179870L,179876L,179881L,179889L,179894L,179895L,179906L,179907L,179910L,179911L,179916L,179917L,179923L,179925L,179929L,179933L,179934L,179938L,179949L,179956L,179959L,179972L,179974L,179982L,179987L,179988L,179991L,179994L,179995L,180010L,180016L,180025L,180027L,180033L,180037L,180049L,180050L,180057L,180064L,180065L,180068L,180070L,180071L,180073L,180075L,180079L,180081L,180091L,180097L,180099L,180105L,180111L,180118L,180120L,180124L,180126L,180128L,180129L,180136L,180138L,180152L,180157L,180158L,180169L,180170L,180173L,180174L,180175L,180180L,180186L,180188L,180193L,180200L,180208L,180212L,180219L,180226L,180228L,180235L,180244L,180259L,180269L,180284L,180290L,180307L,180309L,180311L,180327L,180330L,180335L,180336L,180338L,180340L,180341L,180344L,180346L,180350L,180362L,180363L,180364L,180369L,180371L,180373L,180374L,180375L,180378L,180384L,180386L,180390L,180393L,180394L,180405L,180408L,180409L,180418L,180430L,180438L,180443L,180449L,180453L,180456L,180457L,180459L,180465L,180467L,180472L,180483L,180499L,180500L,180510L,180522L,180525L,180526L,180530L,180533L,180536L,180541L,180544L,180545L,180549L,180558L,180560L,180587L,180588L,180590L,180591L,180596L,180597L,180598L,180603L,180604L,180608L,180609L,180610L,180621L,180635L,180636L,180638L,180643L,180649L,180650L,180658L,180678L,180679L,180689L,180693L,180694L,180701L,180706L,180707L,180711L,180715L,180727L,180735L,180738L,180741L,180742L,180750L,180753L,180757L,180759L,180777L,180779L,180783L,180787L,180789L,180792L,180794L,180797L,180799L,180803L,180814L,180846L,180849L,180854L,180857L,180861L,180878L,180879L,180892L,180893L,180894L,180895L,180901L,180913L,180925L,180944L,180947L,180956L,180957L,180958L,180960L,180964L,180966L,180967L,180970L,180975L,180977L,180987L,180993L,180995L,181003L,181004L,181019L,181021L,181025L,181026L,181027L,181029L,181031L,181036L,181038L,181040L,181044L,181048L,181059L,181062L,181063L,181067L,181086L,181098L,181099L,181101L,181107L,181113L,181115L,181117L,181122L,181123L,181126L,181131L,181139L,181140L,181144L,181146L,181150L,181159L,181162L,181164L,181175L,181179L,181185L,181187L,181188L,181192L,181197L,181198L,181199L,181203L,181210L,181213L,181223L,181230L,181232L,181234L,181247L,181254L,181257L,181263L,181267L,181270L,181272L,181279L,181280L,181281L,181290L,181292L,181307L,181318L,181321L,181329L,181331L,181336L,181337L,181340L,181341L,181344L,181348L,181350L,181354L,181355L,181356L,181357L,181359L,181360L,181361L,181363L,181365L,181372L,181373L,181377L,181378L,181379L,181381L,181382L,181385L,181391L,181398L,181407L,181409L,181416L,181418L,181419L,181421L,181422L,181438L,181440L,181443L,181455L,181458L,181462L,181475L,181477L,181478L,181495L,181496L,181497L,181507L,181512L,181518L,181521L,181524L,181525L,181528L,181531L,181535L,181540L,181541L,181546L,181549L,181554L,181555L,181556L,181557L,181559L,181566L,181568L,181572L,181573L,181574L,181585L,181592L,181597L,181598L,181600L,181603L,181605L,181607L,181612L,181621L,181624L,181629L,181630L,181638L,181645L,181650L,181659L,181662L,181678L,181681L,181691L,181693L,181698L,181705L,181710L,181715L,181717L,181725L,181727L,181731L,181734L,181747L,181755L,181757L,181764L,181767L,181774L,181785L,181794L,181801L,181802L,181804L,181808L,181810L,181819L,181820L,181822L,181846L,181854L,181859L,181863L,181865L,181868L,181871L,181873L,181874L,181882L,181883L,181888L,181905L,181908L,181913L,181915L,181925L,181930L,181931L,181935L,181936L,181940L,181945L,181947L,181952L,181959L,181964L,181967L,181975L,181984L,181985L,181991L,181992L,181993L,181994L,181995L,181998L,182000L,182001L,182005L,182012L,182023L,182027L,182032L,182037L,182038L,182047L,182052L,182054L,182057L,182058L,182059L,182071L,182083L,182087L,182090L,182091L,182092L,182110L,182111L,182119L,182124L,182130L,182131L,182133L,182134L,182135L,182139L,182153L,182155L,182156L,182160L,182165L,182172L,182175L,182178L,182187L,182189L,182191L,182206L,182213L,182223L,182235L,182236L,182241L,182242L,182247L,182255L,182257L,182261L,182264L,182267L,182269L,182273L,182274L,182287L,182292L,182299L,182301L,182306L,182307L,182319L,182323L,182327L,182338L,182339L,182351L,182352L,182354L,182358L,182364L,182369L,182370L,182377L,182379L,182382L,182383L,182387L,182392L,182398L,182403L,182405L,182409L,182416L,182417L,182419L,182421L,182427L,182431L,182433L,182447L,182449L,182450L,182454L,182457L,182458L,182463L,182465L,182467L,182472L,182474L,182485L,182486L,182488L,182490L,182500L,182502L,182505L,182510L,182512L,182520L,182523L,182529L,182536L,182539L,182549L,182552L,182555L,182558L,182569L,182570L,182572L,182573L,182576L,182582L,182584L,182588L,182591L,182593L,182594L,182603L,182612L,182618L,182626L,182635L,182646L,182647L,182652L,182654L,182656L,182657L,182658L,182659L,182668L,182674L,182677L,182681L,182687L,182693L,182695L,182696L,182708L,182709L,182716L,182718L,182719L,182726L,182727L,182756L,182757L,182760L,182771L,182775L,182781L,182783L,182800L,182801L,182805L,182809L,182811L,182817L,182822L,182825L,182826L,182832L,182835L,182840L,182846L,182847L,182855L,182859L,182866L,182867L,182869L,182870L,182871L,182874L,182875L,182876L,182879L,182882L,182893L,182895L,182897L,182898L,182903L,182904L,182906L,182908L,182910L,182912L,182914L,182916L,182919L,182921L,182925L,182930L,182940L,182941L,182943L,182945L,182946L,182947L,182950L,182951L,182958L,182960L,182964L,182965L,182971L,182972L,182973L,182974L,182980L,182986L,182990L,182995L,183008L,183025L,183027L,183031L,183039L,183044L,183051L,183053L,183060L,183065L,183068L,183097L,183098L,183105L,183114L,183117L,183126L,183132L,183137L,183138L,183143L,183149L,183155L,183161L,183162L,183165L,183173L,183180L,183200L,183209L,183211L,183215L,183218L,183221L,183231L,183249L,183267L,183277L,183278L,183286L,183288L,183291L,183294L,183305L,183310L,183318L,183324L,183325L,183334L,183343L,183349L,183350L,183355L,183358L,183365L,183390L,183395L,183409L,183414L,183428L,183439L,183441L,183446L,183455L,183461L,183480L,183481L,183483L,183484L,183486L,183490L,183494L,183503L,183512L,183513L,183515L,183520L,183522L,183524L,183537L,183550L,183558L,183559L,183571L,183584L,183592L,183599L,183601L,183606L,183609L,183615L,183616L,183618L,183632L,183636L,183640L,183646L,183650L,183653L,183657L,183658L,183661L,183664L,183669L,183671L,183672L,183676L,183684L,183691L,183699L,183703L,183709L,183714L,183716L,183717L,183722L,183729L,183731L,183732L,183736L,183737L,183738L,183739L,183744L,183747L,183749L,183750L,183751L,183752L,183756L,183758L,183760L,183761L,183762L,183764L,183766L,183769L,183774L,183775L,183776L,183777L,183779L,183780L,183781L,183782L,183783L,183784L,183785L,183789L,183790L,183791L,183792L,183794L,183796L,183797L,183798L,183802L,183804L,183807L,183808L,183809L,183812L,183813L,183815L,183817L,183818L,183819L,183821L,183823L,183824L,183827L,183828L,183829L,183831L,183833L,183836L,183837L,183841L,183842L,183843L,183844L,183845L,183846L,183847L,183849L,183851L,183853L,183854L,183857L,183858L,183859L,183861L,183864L,183865L,183866L,181383L,181433L,181565L,181825L,181849L,182751L,182900L,183184L,176257L,181544L,181879L,182537L,182585L,182808L,183561L);
//        List<Long> list = Arrays.asList(183821L);
        List<Long> list = Arrays.asList(30048L);
        for (Long aLong : list) {
            try {
                Thread.sleep(500L);
                DenyOwnerQualificationDTO deny = new DenyOwnerQualificationDTO();
                deny.setId(aLong);
                deny.setReason("您好，《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）已于3月1日实施，因购车资格的要求有调整，请重新提交“购车资格查询”事项。");
                deny.setOperatorId("-2");
                deny.setOperatorName("手动更改3月1日前审核中和复核");
                ownerQualificationService.denyOwnerQualificationManualTest(deny);
            } catch (InterruptedException e) {
                e.printStackTrace();
                System.out.println("此次循环出现错误" + aLong);
            }
        }
//        DenyOwnerQualificationDTO denyOwnerQualificationDTO = new DenyOwnerQualificationDTO();
//
//        qualificationService.denyOwnerQualificationManualTest(List);
    }

    @Test
    public void pushTrafficCommittee() {
        trafficPushService.trafficCommitteePush();
    }

    public static void main(String[] args) {
//        long time = DateUtil.parse("20210301", DateUtil.DATE_TYPE4).getTime();
//        System.out.println(time);

        Map<String, String> map = new HashMap<>();
        map.put("appKey", "1846097519839416320");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("signature", ShaUtil.shaEncode(map.get("appKey") + "o6ZutTmf" + map.get("timestamp")));
        map.put("serviceCode", "TINwsN3hLIOPabhH");
        System.out.println(JSON.toJSONString(map));
    }

    @Test
    public void getFileInfo() {
        List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, 200571L);
        System.out.println(fileInfo.isEmpty());
        System.out.println(JSON.toJSONString(fileInfo));
    }

    @Test
    public void ownerInfo() {
        SearchOwnerConfirmListDTO searchOwnerConfirmListDTO = new SearchOwnerConfirmListDTO();
        searchOwnerConfirmListDTO.setPageNum(1L);
        searchOwnerConfirmListDTO.setPageSize(10L);
        searchOwnerConfirmListDTO.setChargeDeviceProperty(1);
        PageInfoBO<OwnerConfirmInfoDTO> ownerConfirmInfoDTOPageInfoBO = ownerService.searchOwnerConfirmList(searchOwnerConfirmListDTO);
        System.out.println(JSON.toJSONString(ownerConfirmInfoDTOPageInfoBO));
    }

    @Test
    public void ownerInfoSecond() {
        SearchSecondHandVehicleAppliedOwnerDTO searchOwnerConfirmListDTO = new SearchSecondHandVehicleAppliedOwnerDTO();
        searchOwnerConfirmListDTO.setLimit(10L);
        searchOwnerConfirmListDTO.setOffset(1L);
        searchOwnerConfirmListDTO.setChargeDeviceProperty(1);
        PageInfoBO<SecondHandVehicleAppliedOwnerDTO> secondHandVehicleAppliedOwnerDTOPageInfoBO = ownerService.searchSecondHandVehicleAppliedOwnerList(searchOwnerConfirmListDTO);
        System.out.println(JSON.toJSONString(secondHandVehicleAppliedOwnerDTOPageInfoBO));
    }

    @Test
    public void searchOwnerTrafficQualification() {
        SearchOwnerTrafficQualificationDTO searchOwnerTrafficQualificationDTO = new SearchOwnerTrafficQualificationDTO();
        searchOwnerTrafficQualificationDTO.setPageNum(1L);
        searchOwnerTrafficQualificationDTO.setPageSize(10L);
        searchOwnerTrafficQualificationDTO.setDriverLicenseCode("320481199606055475");
        PageInfoBO<OwnerTrafficQualificationDTO> ownerTrafficQualificationDTOPageInfoBO = ownerTrafficQualificationService.searchOwnerTrafficQualification(searchOwnerTrafficQualificationDTO);
        System.out.println(JSON.toJSONString(ownerTrafficQualificationDTOPageInfoBO));
    }

    @Test
    public void queryDriverLicenseInfoByDriverLicenseCode() {
        Map<String, String> map = new HashMap<>();
        map.put("appKey", "98625141753466e54");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("signature", ShaUtil.shaEncode(map.get("appKey") + "8HXTeWHk" + map.get("timestamp")));
        map.put("serviceCode", "aUdU477pHWMunxLb");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM","341622199202032315");

        String response = restTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_LICENSE, header, params, String.class, map);
        System.out.println(response);
        TrafficBaseResponse<String> driveInfo = JsonUtil.readStr(response, new TypeReference<TrafficBaseResponse<String>>() {});
        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(driveInfo.getData(), new TypeReference<List<DriverLicenseInfo>>() {});
        DriverLicenseInfo result = null;
        if(driverLicenseInfoList.size() > 0){
            result = driverLicenseInfoList.get(0);
            //如果查询到多个驾照信息，则优先获取有效的驾照
            if(driverLicenseInfoList.size() > 1){
                for(DriverLicenseInfo driverLicenseInfo : driverLicenseInfoList){
                    //校验驾照状态
                    if(isDriverLicenseStatusValid(driverLicenseInfo.getZt())){
                        result = driverLicenseInfo;
                        break;
                    }
                }
            }
        }
        System.out.println(JSON.toJSONString(result));
    }

    public Boolean isDriverLicenseStatusValid(String zt){
        Boolean result = true;
        String[] invalidStatusArray = new String[]{"B","C","D","E","F","G","J","K","M","R","S","U"};
        for (String invalidStatus : invalidStatusArray){
            if(zt.contains(invalidStatus)){
                result = false;
                break;
            }
        }
        return result;
    }

    @Test
    public void testSql() {
//        OwnerInfo ownerInfo = ownerInfoMapper.selectBySearchSecondHandVehicleAppliedOwnerDTO();
//        System.out.println(JSON.toJSONString(ownerInfo));
//        int i = ownerQualificationMapper.select1();
//        int b = ownerInfoMapper.select1();
//        System.out.println(b);
        SearchSecondHandVehicleAppliedOwnerDTO search = new SearchSecondHandVehicleAppliedOwnerDTO();
        search.setLimit(10L);
        search.setOffset(1L);
        search.setReviewStartTime(DateUtil.parse("2021-03-01", DateUtil.DATE_TYPE3));
//        search.setReviewEndTime(new Date());
        PageInfoBO<SecondHandVehicleAppliedOwnerDTO> secondHandVehicleAppliedOwnerDTOPageInfoBO = ownerService.searchSecondHandVehicleAppliedOwnerList(search);
        System.out.println(JSON.toJSONString(secondHandVehicleAppliedOwnerDTOPageInfoBO));
    }

    @Test
    public void reviewOwnerQualification() {
//        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(211306L);
//        if(optionalOwnerQualification.isPresent() && optionalOwnerQualification.get().getApplyStatus() == 0){
//            ownerQualificationService.autoReviewOwnerQualification(optionalOwnerQualification.get());
//        }

        ownerQualificationService.autoReviewOwnerQualification();
    }


    @Test
    public void ElectronicIdLogin(){
        //登录获取token
        String url = "https://zwdtcert.sh.gov.cn/zzk/cert/usage/login.do";
        Map params = new HashMap();
        params.put("account", "jxwxnyyz");
        params.put("password", "pPHCxQGPo8wAO7y");
        final Map results = restTemplate.postForObject(url, params, Map.class);
        System.out.println(JSON.toJSONString(results));
    }



    /**
     * 身份证信息校验
     */
    @Test
    public void checkIDCard(){
        String url = "https://zwdtcert.sh.gov.cn/zzk/cert/usage/checkIDCard.do";

        String sessionId = "159642e7-ed8d-4b38-beae-110f97b8527f";

//        String name = "吴卫";
//        String authId = "******************";

//        String name = "沈华鑫";
//        String authId = "360429199804051012";

//        String name = "蒋长瑜";
//        String authId = "310115199306110418";

        String name = "陈爱国";
        String authId = "321281198910011077";



        Map params = new HashMap();
        params.put("sessionId", sessionId);
        params.put("name", name);
        params.put("id", authId);
//        params.put("startDay","2016-02-02");
//        params.put("endDay", "2036-02-02");
        params.put("orgName", "上海市经济和信息化委员会");
        params.put("username","线上办件");
        params.put("itemName","上海市新能源汽车专用牌照申领“一件事”-购车资格查询（查询）");
        params.put("itemCode","113100000024220045231200259600001");
        params.put("businessCode","001003519500001");

        System.out.println(JSON.toJSONString(params));
        final CheckIDCardResponse results = restTemplate.postForObject(url, params, CheckIDCardResponse.class);
        System.out.println(JSON.toJSONString(results));
    }

    /**
     * 根据证照编码获取证照信息
     */
    @Test
    public void secQueryCertBaseData(){
        String url = "https://zwdtcert.sh.gov.cn/zzk/cert/usage/secQueryCertBaseData.do";

        String sessionId = "159642e7-ed8d-4b38-beae-110f97b8527f";
        String idCard = "FLaE+KvAhmRGzVgMkMd5Gk/dYkTnqqr052PbH06HMZYZOC4IwlN6mRzcwkvmemRd";

//        String idCard = "vjwPd3ToBzL6rsk5Vp6JI96nBKZxup42I2Bz5XwuVW8ZOC4IwlN6mRzcwkvmemRd";

        Map params = new HashMap();
        params.put("sessionId", sessionId);
        params.put("catMainCode", "310105117000100");

        params.put("orgName", "上海市经济和信息化委员会");
        params.put("holderCode", idCard);
        params.put("username","线上办件");
        params.put("itemName","上海市新能源汽车专用牌照申领“一件事”-购车资格查询（查询）");
        params.put("itemCode","113100000024220045231200259600001");
        params.put("businessCode","001003519500001");


        final List<SecQueryCertBaseDataResponse> results = restTemplate.postForObject(url, params, List.class);
        System.out.println(JSON.toJSONString(results));

    }


    @Test
    public void queryCertBaseData(){
        String url = "https://zwdtcert.sh.gov.cn/zzk/cert/usage/queryCertBaseData.do";

        String sessionId = "8e7a0473-1de2-45f8-a989-689f4a3fc78a";

        String name = "蒋长瑜";
        String authId = "310115199306110418";

        Map params = new HashMap();
        params.put("sessionId", sessionId);
        params.put("catMainCode", "310105117000100");
        params.put("certCode",null);
        params.put("holderType","居民身份证");
        params.put("holderCode",authId);
        params.put("holderName", name);

        params.put("orgName", "上海市经济和信息化委员会");
        params.put("username","线上办件");
        params.put("itemName","上海市新能源汽车专用牌照申领“一件事”-购车资格查询（查询）");
        params.put("itemCode","113100000024220045231200259600001");
        params.put("businessCode","001003519500001");

        System.out.println(JSON.toJSONString(params));
        final String results = restTemplate.postForObject(url, params, String.class);
        System.out.println(JSON.toJSONString(results));
    }




}