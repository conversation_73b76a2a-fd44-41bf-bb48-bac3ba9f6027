package com.extracme.nevmp.service.qulification;

import static com.extracme.nevmp.mapper.OwnerQualificationDynamicSqlSupport.ownerQualification;
import static org.junit.Assert.assertTrue;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;

import org.junit.After;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvFileSource;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.event.annotation.AfterTestMethod;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.dto.gov.traffic.OwnerVehicleInfoDTO;
import com.extracme.nevmp.dto.owner.DriverLicenseDTO;
import com.extracme.nevmp.dto.owner.QueryOwnerSocialDetailDTO;
import com.extracme.nevmp.dto.owner.QueryOwnerSocialDetailResponse;
import com.extracme.nevmp.dto.qualification.owner.OwnerQualificationDetailDTO;
import com.extracme.nevmp.dto.qualification.owner.SaveBusinessOwnerQualificationDTO;
import com.extracme.nevmp.dto.qualification.owner.SaveMilitaryQualificationInfoDTO;
import com.extracme.nevmp.dto.qualification.owner.SavePrivateOwnerQualificationDTO;
import com.extracme.nevmp.enums.AuthTypeEnum;
import com.extracme.nevmp.enums.HouseholdRegistrationEnum;
import com.extracme.nevmp.enums.OwnerQualificationStatusEnum;
import com.extracme.nevmp.mapper.OwnerQualificationMapper;
import com.extracme.nevmp.mapper.OwnerQualificationReviewDetailMapper;
import com.extracme.nevmp.model.OwnerQualification;
import com.extracme.nevmp.model.OwnerQualificationReviewDetail;
import com.extracme.nevmp.model.OwnerTrafficQualification;
import com.extracme.nevmp.service.UserOperateLogService;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.library.LibraryService;
import com.extracme.nevmp.service.qualification.CreditService;
import com.extracme.nevmp.service.qualification.DriverService;
import com.extracme.nevmp.service.qualification.HouseholdRegistrationService;
import com.extracme.nevmp.service.qualification.NewSocialService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.service.qualification.SocialService;
import com.extracme.nevmp.service.qualification.impl.DriverServiceImpl;
import com.extracme.nevmp.utils.DateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 */
@Slf4j
@DisplayName("购车资格服务测试类")
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.NONE)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class OwnerQualificationServiceTest {

    // ==================== 测试常量定义 ====================

    // 测试用户信息常量
    private static final String TEST_PRIVATE_USER_NAME = "孙克遥";
    private static final String TEST_PRIVATE_USER_ID_CARD = "320602199011052537";
    private static final String TEST_PRIVATE_USER_BIRTHDAY = "1993-06-11";
    private static final String TEST_BUSINESS_USER_NAME = "上海万炼实业有限公司";
    private static final String TEST_BUSINESS_USER_CREDIT_CODE = "91310113MA1GKBNL0J";
    private static final String TEST_FAKE_USER_NAME = "蒋长瑜2";
    private static final String TEST_FAKE_USER_ID_CARD = "310114199306110418";

    // 驾照信息常量
    private static final String TEST_DRIVER_LICENSE_CODE = "310115199306110418";
    private static final String TEST_DRIVER_LICENSE_ISSUING_PLACE = "上海市";
    private static final String TEST_DRIVER_LICENSE_ISSUING_ORGANIZATION = "上海浦东公安局";

    // 测试用户会话常量
    private static final String TEST_USER_ID = "-1";
    private static final String TEST_USER_NAME = "unit test";
    private static final String TEST_ORG_ID = "09b5b3c6-f00b-4d6c-b1b8-ebb4968e267b";
    private static final String TEST_ORG_NAME = "比亚迪汽车有限公司";

    // 业务常量
    private static final Integer PRIVATE_OWNER_PROPERTY = 1;
    private static final Integer BUSINESS_OWNER_PROPERTY = 2;
    private static final Long TEST_QUALIFICATION_ID = 589L;

    // ==================== 依赖注入 ====================

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @Autowired
    private SocialService socialService;

    @Autowired
    private NewSocialService newSocialService;

    @Autowired
    private CreditService creditService;

    @Autowired
    private DriverService driverService;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private OwnerQualificationMapper ownerQualificationMapper;

    @Autowired
    private OwnerQualificationReviewDetailMapper ownerQualificationReviewDetailMapper;

    @Autowired
    private UserOperateLogService userOperateLogService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private HouseholdRegistrationService householdRegistrationService;

    // ==================== 测试数据 ====================

    private static UserSession userSession;

    private static OwnerQualification privateOwnerQualification;

    private static OwnerQualification businessOwnerQualification;

    private static OwnerTrafficQualification ownerTrafficQualification;

    private static OwnerQualification fakeOwnerQualification;

    private static OwnerTrafficQualification fakeOwnerTrafficQualification;




    // ==================== 测试初始化方法 ====================

    /**
     * 初始化测试数据
     * 设置测试用户会话信息
     */
    @BeforeAll
    public static void init(){
        log.info("初始化购车资格服务测试数据");
        userSession = createTestUserSession();
    }

    /**
     * 创建测试用户会话
     * @return 测试用户会话对象
     */
    private static UserSession createTestUserSession() {
        UserSession session = new UserSession();
        session.setUserId(TEST_USER_ID);
        session.setUserName(TEST_USER_NAME);
        session.setOrgId(TEST_ORG_ID);
        session.setOrgName(TEST_ORG_NAME);
        return session;
    }

    /**
     * 创建私人用户购车资格测试数据
     * @return 私人用户购车资格对象
     */
    private OwnerQualification createPrivateOwnerQualification() {
        OwnerQualification qualification = new OwnerQualification();
        qualification.setName(TEST_PRIVATE_USER_NAME);
        qualification.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        qualification.setAuthId(TEST_PRIVATE_USER_ID_CARD);
        qualification.setBirthDay(DateUtil.parse(TEST_PRIVATE_USER_BIRTHDAY, DateUtil.DATE_TYPE3));
        qualification.setHouseholdRegistrationType(HouseholdRegistrationEnum.SHANGHAI.getValue());
        qualification.setProperty(PRIVATE_OWNER_PROPERTY);
        return qualification;
    }

    /**
     * 创建企业用户购车资格测试数据
     * @return 企业用户购车资格对象
     */
    private OwnerQualification createBusinessOwnerQualification() {
        OwnerQualification qualification = new OwnerQualification();
        qualification.setName(TEST_BUSINESS_USER_NAME);
        qualification.setAuthType(AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType());
        qualification.setAuthId(TEST_BUSINESS_USER_CREDIT_CODE);
        qualification.setProperty(BUSINESS_OWNER_PROPERTY);
        return qualification;
    }

    /**
     * 创建虚假用户购车资格测试数据
     * @return 虚假用户购车资格对象
     */
    private OwnerQualification createFakeOwnerQualification() {
        OwnerQualification qualification = new OwnerQualification();
        qualification.setName(TEST_FAKE_USER_NAME);
        qualification.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        qualification.setAuthId(TEST_FAKE_USER_ID_CARD);
        qualification.setBirthDay(DateUtil.parse(TEST_PRIVATE_USER_BIRTHDAY, DateUtil.DATE_TYPE3));
        qualification.setHouseholdRegistrationType(HouseholdRegistrationEnum.SHANGHAI.getValue());
        qualification.setProperty(PRIVATE_OWNER_PROPERTY);
        return qualification;
    }

    /**
     * 创建交通资格测试数据
     * @return 交通资格对象
     */
    private OwnerTrafficQualification createOwnerTrafficQualification() {
        OwnerTrafficQualification qualification = new OwnerTrafficQualification();
        qualification.setDriverLicenseCode(TEST_DRIVER_LICENSE_CODE);
        qualification.setDriverLicenseIssuingPlace(TEST_DRIVER_LICENSE_ISSUING_PLACE);
        qualification.setDriverLicenseIssuingOrganization(TEST_DRIVER_LICENSE_ISSUING_ORGANIZATION);
        return qualification;
    }

    @Test
    public void test(){
        List<OwnerVehicleInfoDTO> ownerVehicleInfoList = driverService.queryDriverCurrentVehicleList("130202198403210088");
        System.out.println(JSON.toJSONString(ownerVehicleInfoList));
        //名下是否存在非新能源车辆 或采用大牌额度的新能源车辆
        if(ownerVehicleInfoList.stream().anyMatch(ownerVehicleInfo -> (!ownerVehicleInfo.isNewEnergy() || ownerVehicleInfo.getCarQuota() == 2))){
            System.out.println("通过");
        }else{
            System.out.println("名下暂未查询到有效的大牌额度车辆信息");
        }
    }

    @Test
    @Order(0)
    @DisplayName("初始化数据")
    public void setUp(){
        //清空测试记录
//        ownerQualificationMapper.delete(SqlBuilder.deleteFrom(OwnerQualificationDynamicSqlSupport.ownerQualification).build().render(RenderingStrategies.MYBATIS3));
//        ownerQualificationReviewDetailMapper.delete(SqlBuilder.deleteFrom(OwnerQualificationReviewDetailDynamicSqlSupport.ownerQualificationReviewDetail).build().render(RenderingStrategies.MYBATIS3));
    }


    // ==================== 测试数据初始化 ====================

    /**
     * 创建意向用户测试数据
     * 包括私人用户、企业用户和虚假用户的购车资格数据
     */
    @Test
    @Order(1)
    @DisplayName("创建意向用户测试数据")
    public void should_CreateTestData_When_InitializingTestEnvironment(){
        log.info("开始创建购车资格测试数据");

        // 创建并保存私人用户购车资格
        createAndSavePrivateOwnerQualification();

        // 创建交通资格数据
        createTrafficQualificationData();

        // 创建并保存企业用户购车资格
        createAndSaveBusinessOwnerQualification();

        // 创建并保存虚假用户购车资格
        createAndSaveFakeOwnerQualification();

        log.info("购车资格测试数据创建完成");
    }

    /**
     * 创建并保存私人用户购车资格
     */
    private void createAndSavePrivateOwnerQualification() {
        OwnerQualification qualification = createPrivateOwnerQualification();
        ownerQualificationMapper.insertSelective(qualification);
        assertTrue("私人用户购车资格应该保存成功",
                ownerQualificationMapper.selectByPrimaryKey(qualification.getId()).isPresent());
        OwnerQualificationServiceTest.privateOwnerQualification = qualification;
        log.info("私人用户购车资格创建成功，ID: {}", qualification.getId());
    }

    /**
     * 创建交通资格数据
     */
    private void createTrafficQualificationData() {
        OwnerTrafficQualification qualification = createOwnerTrafficQualification();
        OwnerQualificationServiceTest.ownerTrafficQualification = qualification;
        log.info("交通资格数据创建成功");
    }

    /**
     * 创建并保存企业用户购车资格
     */
    private void createAndSaveBusinessOwnerQualification() {
        OwnerQualification qualification = createBusinessOwnerQualification();
        ownerQualificationMapper.insertSelective(qualification);
        assertTrue("企业用户购车资格应该保存成功",
                ownerQualificationMapper.selectByPrimaryKey(qualification.getId()).isPresent());
        OwnerQualificationServiceTest.businessOwnerQualification = qualification;
        log.info("企业用户购车资格创建成功，ID: {}", qualification.getId());
    }

    /**
     * 创建并保存虚假用户购车资格
     */
    private void createAndSaveFakeOwnerQualification() {
        OwnerQualification qualification = createFakeOwnerQualification();
        ownerQualificationMapper.insertSelective(qualification);
        assertTrue("虚假用户购车资格应该保存成功",
                ownerQualificationMapper.selectByPrimaryKey(qualification.getId()).isPresent());
        OwnerQualificationServiceTest.fakeOwnerQualification = qualification;
        log.info("虚假用户购车资格创建成功，ID: {}", qualification.getId());
    }

    // ==================== 购车资格详情查询测试 ====================

    /**
     * 测试根据ID获取购车资格详情
     * 验证能够正确获取指定ID的购车资格详细信息
     */
    @Test
    @DisplayName("根据ID获取购车资格详情")
    public void should_ReturnQualificationDetail_When_QueryByValidId(){
        // Given: 给定有效的购车资格ID
        Long qualificationId = TEST_QUALIFICATION_ID;

        // When: 调用获取购车资格详情方法
        OwnerQualificationDetailDTO result = ownerQualificationService.getOwnerQualificationDetail(qualificationId);

        // Then: 验证返回结果
        log.info("购车资格详情查询结果: {}", JSON.toJSONString(result));
        // 这里可以添加更多的断言来验证返回的数据
    }

    @Test
    @DisplayName("查询社保")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testNewSocialQualification(){
//        newSocialService.review(-1L, "上海帮楚自动化科技有限公司"
//                , AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType(), "91310112086216389G");

//
//        newSocialService.review(-1L, "孙均"
//                , AuthTypeEnum.IDENTIFICATION_CARD.getType(), "321282198404263235");

//        newSocialService.review(-1L, "刘巾葛格"
//                , AuthTypeEnum.IDENTIFICATION_CARD.getType(), "230822199308106422");

//        newSocialService.review(-1L, "蒋长瑜", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "310115199306110418");

//        newSocialService.review(-1L, "王苏熠", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "320113199202076445");

//        newSocialService.reviewSocial2025( "蒋长瑜", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "310115199306110418",36, 48);

//        newSocialService.reviewSocial2025( "余伟", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "321281199203261717",36, 48);

//        newSocialService.reviewSocial("蒋长瑜", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "310115199306110418",36);





        QueryOwnerSocialDetailDTO queryOwnerSocialDetailDTO = new QueryOwnerSocialDetailDTO();
        queryOwnerSocialDetailDTO.setName("王文高");
        queryOwnerSocialDetailDTO.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        queryOwnerSocialDetailDTO.setAuthId("321025197205118614");
        QueryOwnerSocialDetailResponse response = ownerQualificationService.queryOwnerSocialDetail(queryOwnerSocialDetailDTO);
        System.out.println(JSON.toJSONString(response));


        queryOwnerSocialDetailDTO = new QueryOwnerSocialDetailDTO();
        queryOwnerSocialDetailDTO.setName("王文高");
        queryOwnerSocialDetailDTO.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        queryOwnerSocialDetailDTO.setAuthId("321025197205118614");
        queryOwnerSocialDetailDTO.setStartTime(DateUtil.parse("2023-01-01 00:00:00", DateUtil.DATE_TYPE1));
        queryOwnerSocialDetailDTO.setEndTime(DateUtil.parse("2024-01-01 00:00:00", DateUtil.DATE_TYPE1));

        response = ownerQualificationService.queryOwnerSocialDetail(queryOwnerSocialDetailDTO);
        System.out.println(JSON.toJSONString(response));


//        NewSocialServiceImpl.SocialResponse response = newSocialService.reviewSocialFromSocialAPI2025("周雪娟", AuthTypeEnum.IDENTIFICATION_CARD.getType()
//                , "320211199003161922", "202301", "202312");
//        System.out.println(JSON.toJSONString(response));


//        NewSocialServiceImpl.SocialResponse response1 = newSocialService.reviewSocialFromSocialAPI("周雪娟", AuthTypeEnum.IDENTIFICATION_CARD.getType()
//                , "320211199003161922", 6);
//        System.out.println(JSON.toJSONString(response1));

    }

    @Test
    @DisplayName("查询用户户籍信息")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testHouseholdRegistrationQualification(){
        boolean result = householdRegistrationService.isLocalHousehold("刘方德", "310103196912040457");
        System.out.println(result);
    }

    @Test
    @DisplayName("查询社保")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testSocialQualification(){
        socialService.review(privateOwnerQualification.getId(), privateOwnerQualification.getName()
                , privateOwnerQualification.getAuthType(), privateOwnerQualification.getAuthId());

        socialService.review(fakeOwnerQualification.getId(), fakeOwnerQualification.getName()
                , fakeOwnerQualification.getAuthType(), fakeOwnerQualification.getAuthId());
    }


    @Test
    @DisplayName("查询信用")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testCreditQualification(){
//        creditService.review(privateOwnerQualification.getId(), privateOwnerQualification.getProperty()
//                , privateOwnerQualification.getName(), privateOwnerQualification.getAuthId());
//
//        creditService.review(fakeOwnerQualification.getId(), fakeOwnerQualification.getProperty()
//                , fakeOwnerQualification.getName(), fakeOwnerQualification.getAuthId());

        creditService.review(-1L, 1, "肖杨", "******************");
    }

    @Test
    @DisplayName("查询公安")
    @AfterTestMethod(value = "testCreateSimpleTrafficQualification")
    public void testTrafficQualification(){


    }


    @Test
    @Disabled
    @DisplayName("保存军官信息")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testSaveMilitaryQualificationInfo(){
        SaveMilitaryQualificationInfoDTO saveMilitaryQualificationInfoDTO = new SaveMilitaryQualificationInfoDTO();
        saveMilitaryQualificationInfoDTO.setName("蒋长瑜");
        saveMilitaryQualificationInfoDTO.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        saveMilitaryQualificationInfoDTO.setAuthId("310115199306110418");
        saveMilitaryQualificationInfoDTO.setBirthDay(DateUtil.parse("1993-06-11", DateUtil.DATE_TYPE3));
        saveMilitaryQualificationInfoDTO.setHouseholdRegistrationType(HouseholdRegistrationEnum.SHANGHAI.getValue());
        saveMilitaryQualificationInfoDTO.setDriverLicenseIssuingPlace("上海市");
        saveMilitaryQualificationInfoDTO.setDriverLicenseIssuingOrganization("上海市公安局");
        saveMilitaryQualificationInfoDTO.setDriverLicenseCode("310115199306110418");
        String militaryLicenseFile = "2020//08//10//02//ebb4968e267b_9c767f164a39_1597071883881_840.png";
        saveMilitaryQualificationInfoDTO.setFile(new ArrayList<String>(){{add(militaryLicenseFile);}});
        saveMilitaryQualificationInfoDTO.setCreatedUserId(userSession.getUserId());
        saveMilitaryQualificationInfoDTO.setCreatedUserName(userSession.getUserName());
        ownerQualificationService.saveMilitaryQualificationInfo(saveMilitaryQualificationInfoDTO);
    }

    // ==================== 购车资格保存测试 ====================

    /**
     * 测试保存私人用户购车资格
     * 验证私人用户购车资格信息能够正确保存
     */
    @Test
    @DisplayName("保存私人意向用户资质")
    @AfterTestMethod(value = "should_CreateTestData_When_InitializingTestEnvironment")
    public void should_SaveSuccessfully_When_SavePrivateOwnerQualification(){
        // Given: 准备私人用户购车资格保存DTO
        SavePrivateOwnerQualificationDTO saveDTO = createSavePrivateOwnerQualificationDTO();

        // When: 调用保存私人购车资格方法
        BaseResponse result = ownerQualificationService.savePrivateOwnerQualification(saveDTO);

        // Then: 验证保存结果
        log.info("保存私人购车资格结果: {}", JSON.toJSONString(result));
        // 可以添加断言验证保存是否成功
    }

    /**
     * 测试保存企业用户购车资格
     * 验证企业用户购车资格信息能够正确保存
     */
    @Test
    @DisplayName("保存企业意向用户资质")
    @AfterTestMethod(value = "should_CreateTestData_When_InitializingTestEnvironment")
    public void should_SaveSuccessfully_When_SaveBusinessOwnerQualification(){
        // Given: 准备企业用户购车资格保存DTO
        SaveBusinessOwnerQualificationDTO saveDTO = createSaveBusinessOwnerQualificationDTO();

        // When: 调用保存企业购车资格方法
        BaseResponse result = ownerQualificationService.saveBusinessOwnerQualification(saveDTO);

        // Then: 验证保存结果
        log.info("保存企业购车资格结果: {}", JSON.toJSONString(result));
        // 可以添加断言验证保存是否成功
    }

    /**
     * 创建私人用户购车资格保存DTO
     * @return 私人用户购车资格保存DTO
     */
    private SavePrivateOwnerQualificationDTO createSavePrivateOwnerQualificationDTO() {
        SavePrivateOwnerQualificationDTO saveDTO = new SavePrivateOwnerQualificationDTO();
        saveDTO.setName(privateOwnerQualification.getName());
        saveDTO.setAuthId(privateOwnerQualification.getAuthId());
        saveDTO.setAuthType(privateOwnerQualification.getAuthType());
        saveDTO.setHouseholdRegistrationType(privateOwnerQualification.getHouseholdRegistrationType());

        saveDTO.setDriverFileNo(ownerTrafficQualification.getDriverFileNo());
        saveDTO.setDriverLicenseCode(ownerTrafficQualification.getDriverLicenseCode());
        saveDTO.setDriverLicenseIssuingOrganization(ownerTrafficQualification.getDriverLicenseIssuingOrganization());
        saveDTO.setDriverLicenseIssuingPlace(ownerTrafficQualification.getDriverLicenseIssuingPlace());

        saveDTO.setCreatedUserId(userSession.getUserId());
        saveDTO.setCreatedUserName(userSession.getUserName());
        return saveDTO;
    }

    /**
     * 创建企业用户购车资格保存DTO
     * @return 企业用户购车资格保存DTO
     */
    private SaveBusinessOwnerQualificationDTO createSaveBusinessOwnerQualificationDTO() {
        SaveBusinessOwnerQualificationDTO saveDTO = new SaveBusinessOwnerQualificationDTO();
        saveDTO.setName(businessOwnerQualification.getName());
        saveDTO.setAuthId(businessOwnerQualification.getAuthId());
        saveDTO.setCreatedUserId(userSession.getUserId());
        saveDTO.setCreatedUserName(userSession.getUserName());
        return saveDTO;
    }

    // ==================== 购车资格申请处理测试 ====================

    /**
     * 测试处理遗漏的购车资格申请
     * 验证系统能够正确处理遗漏的购车资格申请
     */
    @Test
    @DisplayName("处理遗漏的购车资格申请")
    public void should_ProcessSuccessfully_When_DealOmitQualificationApply(){
        // When: 调用处理遗漏申请方法
        BaseResponse result = ownerQualificationService.dealOmitQualificationApply();

        // Then: 验证处理结果
        log.info("处理遗漏购车资格申请结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试根据申请编号处理购车资格申请
     * 验证能够正确处理指定申请编号的购车资格申请
     */
    @Test
    @DisplayName("根据申请编号处理购车资格申请")
    public void should_ProcessSuccessfully_When_DealQualificationApplyByApplyNo(){
        // Given: 给定申请编号
        String applyNo = "03W425720000062";

        // When: 调用处理申请方法
        ownerQualificationService.dealQualificationApply(applyNo, null);

        // Then: 验证处理完成
        log.info("处理购车资格申请完成，申请编号: {}", applyNo);
    }

    /**
     * 测试处理补正购车资格申请
     * 验证能够正确处理补正的购车资格申请
     */
    @Test
    @DisplayName("处理补正购车资格申请")
    public void should_ProcessSuccessfully_When_DealSupplementQualificationApply(){
        // Given: 给定补正申请编号
        String applyNo = "03W4257200000C7";

        // When: 调用处理补正申请方法
        ownerQualificationService.dealSupplementQualificationApply(applyNo);

        // Then: 验证处理完成
        log.info("处理补正购车资格申请完成，申请编号: {}", applyNo);
    }

    @Test
    public void testSpecialPending(){
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetails = driverService.querySpecialPendingReview();
        System.out.println(JSON.toJSONString(ownerQualificationReviewDetails));
    }

    @Transactional
    @Rollback
    @Test
    public void testReviewTrafficQualification(){
        Long ownerQualificationId = 1L;
        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode("310115199306110418")
                .driverLicenseIssuingOrganization("上海市车管所")
                .driverLicenseIssuingPlace("上海市")
                .build();
        driverService.review(ownerQualificationId, driverLicense);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/社保补查.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testSocialQuery(Long id, String name,String authId){
        newSocialService.review(id, name
                , AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType(), authId);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/社保无记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testNoSocialResult(String name, String ownerType, String autType, String authId, String nationality
            , String result, String remark){
        System.out.println(name + "-" + authId);
        if(Objects.equals(autType, "身份证")){
            socialService.review(1L,name, AuthTypeEnum.IDENTIFICATION_CARD.getType(),authId);
        }else{
            socialService.review(1L,name, AuthTypeEnum.RESIDENCE_PERMIT.getType(),authId);
        }
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/社保不满一年.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testSocialDeny(String name, String ownerType, String autType, String authId, String nationality
            , String result, String remark){
        System.out.println(name + "-" + authId);
        if(Objects.equals(autType, "身份证")){
            socialService.review(1L,name, AuthTypeEnum.IDENTIFICATION_CARD.getType(),authId);
        }else{
            socialService.review(1L,name, AuthTypeEnum.RESIDENCE_PERMIT.getType(),authId);
        }
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/法人信用记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testBusinessCreditResult(String name, String authId){
        System.out.println(name + "-" + authId);
        creditService.review(1L,2,name,authId);
    }


    @ParameterizedTest
    @CsvFileSource(resources = "/私人信用无记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testPrivateNoCreditResult(String name, String authId){
        System.out.println(name + "-" + authId);
        creditService.review(1L,1,name,authId);

    }

    @ParameterizedTest
    @CsvFileSource(resources = "/私人信用不通过.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testPrivateCreditDeny(String name, String authId){
        System.out.println(name + "-" + authId);
        creditService.review(1L,1,name,authId);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/驾照无记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testNoDriverResult(String name, String authType, String authId){
        System.out.println(name + "-" + authId);

        if("身份证".equals(authType)){
            final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                    .driverLicenseCode(authId)
                    .driverLicenseIssuingOrganization("上海市车管所")
                    .driverLicenseIssuingPlace("上海市")
                    .build();
            driverService.review(1L, driverLicense);
        }else{
            final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                    .driverFileNo(authId)
                    .driverLicenseIssuingOrganization("上海市车管所")
                    .driverLicenseIssuingPlace("上海市")
                    .build();
            driverService.review(1L, driverLicense);
        }

    }


    @ParameterizedTest
    @CsvFileSource(resources = "/违章大于五次.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testDriverIllegal(String name, String authType, String authId) throws InterruptedException {
        System.out.println(name + "-" + authId);
        Long ownerQualificationId = 1L;
        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(authId)
                .driverLicenseIssuingOrganization("上海市车管所")
                .driverLicenseIssuingPlace("上海市")
                .build();
        driverService.review(1L, driverLicense);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/名下有新能源汽车.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testDriverHasVehicle(String name, String authType, String authId) throws InterruptedException {
        System.out.println(name + "-" + authId);
        Long ownerQualificationId = 1L;
        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(authId)
                .driverLicenseIssuingOrganization("上海市车管所")
                .driverLicenseIssuingPlace("上海市")
                .build();
        driverService.review(1L, driverLicense);
        Thread.sleep(5000);
    }

    @Test
    public void testDiver(){
//        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
//                .driverLicenseCode("51122619830816002X")
//                .driverLicenseIssuingOrganization("上海市车管所")
//                .driverLicenseIssuingPlace("上海市")
//                .build();
//        driverService.review(322883L, driverLicense);
        System.out.println(DriverServiceImpl.queryViolationCount("310114197706192613"));

    }


    public static void main(String[] args) {
//        String response = "[{\"SFZMHM\":\"320321198706083928\",\"XM\":\"蒋海婷\",\"DABH\":\"310045951628\",\"YXQZ\":\"2023-01-25 00:00:00\",\"ZT\":\"A\",\"SFZMMC\":\"A\"}]";
//
//        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(response, new TypeReference<List<DriverLicenseInfo>>() {});
//        System.out.println(driverLicenseInfoList);


        int month = 31;
        Calendar instance = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        //从2023年11月开始算起
        instance.set(Calendar.YEAR, 2023);
        instance.set(Calendar.MONTH, Calendar.NOVEMBER);
        instance.add(Calendar.DATE, -1);

        System.out.println(instance.get(Calendar.MONTH));

        instance.set(Calendar.DATE, 1);

        instance.add(Calendar.MONTH, -month +1);

        String yyyyMM = DateUtil.format(instance.getTime(), DateUtil.DATE_TYPE6);

        System.out.println(yyyyMM);

    }

    // ==================== 测试清理方法 ====================

    /**
     * 测试完成后的清理工作
     * 清理测试过程中产生的数据和资源
     */
    @After
    public void destroy(){
        log.info("购车资格服务测试清理完成");
    }

    // ==================== 购车资格查询测试 ====================

    /**
     * 测试根据证件信息查询购车资格详情
     * 验证能够根据证件类型和证件号正确查询购车资格详情
     */
    @Test
    @DisplayName("根据证件信息查询购车资格详情")
    public void should_ReturnQualificationDetail_When_QueryByAuthInfo() {
        // Given: 给定证件类型和证件号
        Integer authType = AuthTypeEnum.IDENTIFICATION_CARD.getType();
        String authId = "310115198405251019";

        // When: 调用查询购车资格详情方法
        OwnerQualificationDetailDTO result = ownerQualificationService.queryOwnerQualificationDetail(authType, authId);

        // Then: 验证查询结果
        log.info("根据证件信息查询购车资格详情结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试查询用户手机号
     * 验证能够根据证件信息正确查询用户手机号
     */
    @Test
    @DisplayName("查询用户手机号")
    public void should_ReturnMobilePhone_When_QueryOwnerMobilePhone() {
        // Given: 给定证件类型和证件号
        int authType = AuthTypeEnum.IDENTIFICATION_CARD.getType();
        String authId = TEST_PRIVATE_USER_ID_CARD;

        // When: 调用查询用户手机号方法
        String mobilePhone = ownerQualificationService.queryOwnerMobilePhone(authType, authId);

        // Then: 验证查询结果
        log.info("查询用户手机号结果: {}", mobilePhone);
    }

    /**
     * 测试查询已通过的购车资格
     * 验证能够正确查询已通过且在有效期内的购车资格
     */
    @Test
    @DisplayName("查询已通过的购车资格")
    public void should_ReturnApprovedQualification_When_QueryApprovedQualification() {
        // Given: 给定证件类型和证件号
        Integer authType = AuthTypeEnum.IDENTIFICATION_CARD.getType();
        String authId = TEST_PRIVATE_USER_ID_CARD;

        // When: 调用查询已通过购车资格方法
        OwnerQualification result = ownerQualificationService.queryApprovedQualification(authType, authId);

        // Then: 验证查询结果
        log.info("查询已通过购车资格结果: {}", JSON.toJSONString(result));
    }

    @Test
    public void queryItemsApplyList() {
//        List<String> itemCodes = new ArrayList<String>() {{
//            add(LibraryApplyItemEnum.OWNER_QUALIFICATION_APPLY.getItemCode());
//        }};
//        QueryItemsApplyListResponse queryItemsApplyListResponse = libraryService.queryItemsApplyList(itemCodes, "待预审", 100);
//        System.out.println(JSON.toJSONString(queryItemsApplyListResponse));

        ownerQualificationService.dealOmitQualificationApply();
    }

    // ==================== 自动审核测试 ====================

    /**
     * 测试处理待审核的购车资格
     * 验证系统能够正确处理待审核状态的购车资格
     */
    @Test
    @DisplayName("处理待审核的购车资格")
    public void should_ProcessSuccessfully_When_DealPendingQualificationReview(){
        // When: 调用处理待审核方法
        BaseResponse result = ownerQualificationService.dealPendingQualificationReview();

        // Then: 验证处理结果
        log.info("处理待审核购车资格结果: {}", JSON.toJSONString(result));
    }

    /**
     * 测试自动审核购车资格
     * 验证系统能够自动审核购车资格信息
     */
    @Test
    @DisplayName("自动审核购车资格")
    public void should_ReviewSuccessfully_When_AutoReviewOwnerQualification(){
        // When: 调用自动审核方法
        BaseResponse result = ownerQualificationService.autoReviewOwnerQualification();

        // Then: 验证审核结果
        log.info("自动审核购车资格结果: {}", JSON.toJSONString(result));
    }



    @ParameterizedTest
    @CsvFileSource(resources = "/购车资质异常数据.csv",numLinesToSkip = 1, encoding = "GBK")
    public void denyAbnormalOwnerQualification(Long id){

        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        if(optionalOwnerQualification.isPresent()){
            String reason = "数据异常，请重新提交申请";
            Date now = new Date();
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                    .set(ownerQualification.reviewTime).equalTo(now)
                    .set(ownerQualification.reason).equalTo(reason)
                    //注:此处需要将过期时间置空，来区分是允许补正的还是直接拒绝的
                    .set(ownerQualification.expireTime).equalToNull()
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo("-1")
                    .set(ownerQualification.updatedUserName).equalTo("管理员")
                    .where(ownerQualification.id, isEqualTo(id))
//                    .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.RECONSIDERATION.getStatus()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);

//            //保存操作日志
//            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
//                    .userOperateTypeEnum(UserOperateTypeEnum.DENY_OWNER_QUALIFICATION)
//                    .reason(reason)
//                    .vehicleId(id)
//                    .operateUserId("-1")
//                    .operateUserName("管理员")
//                    .build();
//            userOperateLogService.saveOperateLog(saveOperateLogDTO);
//
//
//            //更新办件库信息
//            if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
//                asyncService.denyOwnerQualification(optionalOwnerQualification.get().getApplyNo(), reason);
//            }
        }



    }


}
